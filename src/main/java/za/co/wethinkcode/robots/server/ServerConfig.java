package za.co.wethinkcode.robots.server;

/**
 * Configuration class for server command line arguments
 */
public class ServerConfig {
    private int port = 5000;
    private int worldSize = 1;
    private String obstaclePosition = null;

    public static ServerConfig parseArgs(String[] args) {
        ServerConfig config = new ServerConfig();

        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "-p":
                    if (i + 1 < args.length) {
                        config.port = Integer.parseInt(args[i + 1]);
                        // Skip the next argument as it's the value
                        i++;
                    }
                    break;
                case "-s":
                    if (i + 1 < args.length) {
                        config.worldSize = Integer.parseInt(args[i + 1]);
                        i++;
                    }
                    break;
                case "-o":
                    if (i + 1 < args.length) {
                        config.obstaclePosition = args[i + 1];
                        i++;
                    }
                    break;
                default:
                    //Handle single port argument
                    try{
                        int port = Integer.parseInt(args[i]);
                        config.port = port;
                    }catch (NumberFormatException e){
                        //ignore invalid argument
                    }
                    break;
            }
        }

        return config;
    }

    public int getPort() { return port; }
    public int getWorldSize() { return worldSize; }
    public String getObstaclePosition() { return obstaclePosition; }

    // Setter methods for interactive configuration
    public void setPort(int port) { this.port = port; }
    public void setWorldSize(int worldSize) { this.worldSize = worldSize; }
    public void setObstaclePosition(String obstaclePosition) { this.obstaclePosition = obstaclePosition; }
}