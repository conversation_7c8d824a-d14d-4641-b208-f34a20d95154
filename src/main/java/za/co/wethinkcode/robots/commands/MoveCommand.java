package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Position;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.Status;
import za.co.wethinkcode.robots.server.World;

public class MoveCommand extends Command {
    public enum MovementType {
        FORWARD("forward"),
        BACKWARD("back");

        private final String commandName;

        MovementType(String commandName) {
            this.commandName = commandName;
        }

        public String getCommandName() {
            return commandName;
        }
    }

    private final MovementType movementType;

    public MoveCommand(Robot robot, MovementType movementType, String[] arguments) {
        super(robot, arguments);
        this.movementType = movementType;
    }

    @Override
    public String commandName() {
        return movementType.getCommandName();
    }

    @Override
    public Response execute(World world) {
        Steps steps = parseSteps();
        if (steps.isInvalid()) {
            return createErrorResponse("Invalid number of steps.");
        }

        Robot worldRobot = world.findRobot(robot.getName());
        if (worldRobot == null) {
            return createErrorResponse("Robot not found.");
        }

        if (worldRobot.status == Robot.RobotStatus.Dead) {
            return createErrorResponse(worldRobot.getName() + " is DEAD and cannot move.");
        }

        // Check for obstacles in the path first
        Status pathStatus = checkPath(worldRobot, steps, world);
        if (pathStatus == Status.HitObstaclePIT) {
            // Robot dies when hitting a pit - return OK response with "Fell" message
            worldRobot.status = Robot.RobotStatus.Dead;
            Response response = createSuccessResponse("Fell");
            world.stateForRobot(worldRobot, response);
            return response;
        } else if (pathStatus != Status.OK) {
            // Robot is obstructed - return OK response with "Obstructed" message
            Response response = createSuccessResponse("Obstructed");
            world.stateForRobot(worldRobot, response);
            return response;
        }

        // Calculate target position for bounds checking
        Position targetPos = calculateTargetPosition(worldRobot, steps);

        // Check if target is within world bounds
        if (!isWithinWorldBounds(targetPos, world)) {
            // Robot hits edge - return "Obstructed" according to specification
            Response response = createSuccessResponse("Obstructed");
            world.stateForRobot(worldRobot, response);
            return response;
        }

        // Execute movement
        executeMovement(worldRobot, steps);

        // Create success message according to specification
        Response response = createSuccessResponse("Done");
        world.stateForRobot(worldRobot, response);
        return response;
    }

    private Steps parseSteps() {
        try {
            return new Steps(Integer.parseInt(arguments[0]));
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            return Steps.invalid();
        }
    }

    private Position calculateTargetPosition(Robot robot, Steps steps) {
        String moveDirection = getEffectiveDirection(robot);
        return calculatePosition(robot.getPosition(), moveDirection, steps);
    }

    private String getEffectiveDirection(Robot robot) {
        return movementType == MovementType.FORWARD
                ? robot.orientation()
                : oppositeDirection(robot.orientation());
    }

    private boolean isWithinWorldBounds(Position pos, World world) {
        return pos.getX() >= -world.getHalfWidth() &&
                pos.getX() <= world.getHalfWidth() &&
                pos.getY() >= -world.getHalfHeight() &&
                pos.getY() <= world.getHalfHeight();
    }

    private Status checkPath(Robot robot, Steps steps, World world) {
        Position current = robot.getPosition();
        String moveDirection = getEffectiveDirection(robot);

        for (int step = 1; step <= steps.value(); step++) {
            Position nextPos = calculatePosition(current, moveDirection, new Steps(step));
            Status status = world.isPositionValid(nextPos);
            if (status != Status.OK) {
                return status;
            }
        }
        return Status.OK;
    }

    private String oppositeDirection(String direction) {
        return switch (direction) {
            case "NORTH" -> "SOUTH";
            case "SOUTH" -> "NORTH";
            case "EAST" -> "WEST";
            case "WEST" -> "EAST";
            default -> direction;
        };
    }

    private Position calculatePosition(Position start, String direction, Steps steps) {
        return switch (direction) {
            case "NORTH" -> new Position(start.getX(), start.getY() + steps.value());
            case "SOUTH" -> new Position(start.getX(), start.getY() - steps.value());
            case "EAST" -> new Position(start.getX() + steps.value(), start.getY());
            case "WEST" -> new Position(start.getX() - steps.value(), start.getY());
            default -> start;
        };
    }

    private void executeMovement(Robot robot, Steps steps) {
        if (movementType == MovementType.FORWARD) {
            robot.moveForward(steps.value());
        } else {
            robot.moveBackward(steps.value());
        }
    }

    private String getEdgeDirection(Robot robot) {
        return movementType == MovementType.FORWARD
                ? robot.orientation()
                : oppositeDirection(robot.orientation());
    }

    private Steps findMaxSteps(Robot robot, Steps requestedSteps, World world) {
        int maxSteps = 0;
        Position currentPos = robot.getPosition();
        String moveDirection = getEffectiveDirection(robot);

        for (int step = 1; step <= requestedSteps.value(); step++) {
            Position nextPos = calculatePosition(currentPos, moveDirection, new Steps(step));

            if (!isWithinWorldBounds(nextPos, world)) {
                break;
            }

            Status status = world.isPositionValid(nextPos);
            if (status != Status.OK) {
                break;
            }

            maxSteps = step;
        }

        return new Steps(maxSteps);
    }

    private boolean isAtWorldEdge(Robot robot, World world) {
        Position pos = robot.getPosition();
        return pos.getX() == -world.getHalfWidth() || pos.getX() == world.getHalfWidth() ||
                pos.getY() == -world.getHalfHeight() || pos.getY() == world.getHalfHeight();
    }

    private String createMovementMessage(Robot robot, Steps requestedSteps, Steps actualSteps, World world) {
        if (actualSteps.value() < requestedSteps.value()) {
            if (actualSteps.value() == 0 || isAtWorldEdge(robot, world)) {
                return "At the " + getEdgeDirection(robot) + " edge";
            }
        }
        return "Done";
    }

    private Response createSuccessResponse(String message) {
        return new Response("OK", message);
    }

    private Response createErrorResponse(String message) {
        return new Response("ERROR", message);
    }

    private static final class Steps {
        private final int value;
        private final boolean valid;

        private Steps(int value) {
            this.value = value;
            this.valid = value >= 0;
        }

        public static Steps invalid() {
            return new Steps(-1);
        }

        public int value() {
            return value;
        }

        public boolean isInvalid() {
            return !valid;
        }
    }
}